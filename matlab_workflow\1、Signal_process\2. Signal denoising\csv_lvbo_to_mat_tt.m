%CSV_LVBO_TO_MAT_TT CSV信号降噪处理与时间表转换工具
%   批量处理CSV文件，进行双通道信号降噪并转换为MATLAB时间表格式。
%   该脚本集成了预处理、带通滤波和谱减法降噪的完整信号处理流程。
%
%   功能概述:
%   本脚本提供了一个完整的信号处理工作流，从原始CSV数据到降噪后的
%   时间表格式数据，适用于肠鸣音信号、生物医学信号等双通道传感器数据。
%
%   语法:
%   直接运行脚本: csv_lvbo_to_mat_tt
%   (无输入参数，通过GUI选择文件夹)
%
%   输入要求:
%   - CSV文件夹包含多个CSV文件
%   - 每个CSV文件至少包含3列数据 (时间, 通道2, 通道3)
%   - 文件名包含数字用于排序
%   - 采样率固定为2570 Hz
%
%   输出结果:
%   - 在同级目录"2、Processed data"文件夹中生成MAT文件
%   - 每个MAT文件包含两个时间表: tt1, tt2
%   - 文件命名格式: 原文件名_tt.mat
%
%   处理流程:
%   1. 文件夹选择和CSV文件发现
%   2. 按文件名数字进行自然排序
%   3. 逐文件处理:
%      a) 读取CSV数据并提取第2、3列
%      b) 数据预处理 (归一化、去直流、去趋势)
%      c) 100-800Hz带通滤波降噪
%      d) Boll谱减法进一步降噪
%      e) SNR计算和性能评估
%      f) 创建时间表并保存
%
%   技术参数:
%   - 采样率: 2570 Hz (固定)
%   - 带通滤波器: 100-800 Hz
%   - 谱减法参数: IS = 0.15 (静默段比例)
%   - 预处理: 8192归一化因子
%
%   性能指标:
%   - 实时显示每个处理步骤的SNR值
%   - 带通滤波后SNR
%   - 谱减法处理后SNR
%
%   应用场景:
%   - 肠鸣音信号预处理和降噪
%   - 双通道传感器数据批量处理
%   - 生物医学信号质量改善
%   - 音频信号预处理流程
%
%   文件夹结构:
%   输入文件夹/
%   ├── file1.csv, file2.csv, ...
%   └── (自动创建) 2、Processed data/
%       ├── file1_tt.mat
%       ├── file2_tt.mat
%       └── ...
%
%   依赖函数:
%   - preprocessData: 数据预处理函数
%   - SNR_singlech: 信噪比计算函数
%   - SSBoll79: Boll谱减法降噪函数
%   - bandpass: MATLAB内置带通滤波器
%
%   示例用法:
%   1. 准备包含CSV文件的文件夹
%   2. 运行脚本: csv_lvbo_to_mat_tt
%   3. 在弹出的对话框中选择文件夹
%   4. 等待处理完成，查看输出文件夹
%
%   注意事项:
%   - 确保CSV文件格式正确 (至少3列数据)
%   - 处理大量文件时需要足够的磁盘空间
%   - 谱减法处理可能改变信号长度，脚本会自动补零对齐
%   - 建议在处理前备份原始数据
%
%   参见: PREPROCESSDATA, SNR_SINGLECH, SSBOLL79, BANDPASS
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0
%
% =========================================================================

clear all;
clc;
close all;

%% 添加函数路径
% 获取当前脚本所在目录
currentDir = fileparts(mfilename('fullpath'));

% 添加函数文件夹到MATLAB路径
functionDir = fullfile(currentDir, '0、function', '2、Spectral Subtraction');
if exist(functionDir, 'dir')
    addpath(functionDir);
    if exist('createProcessingConfig', 'file') == 2
        fprintf('✓ 成功添加函数路径: %s\n', functionDir);
    else
        warning('函数路径添加失败，请检查文件是否存在');
    end
else
    error('函数文件夹不存在: %s', functionDir);
end

%% 参数配置
% 创建集中的参数配置结构体
config = createProcessingConfig();

%% 选择文件夹并读取csv文件
% 选择文件夹
folderPath = uigetdir('', '选择文件夹'); % 弹出文件夹选择框
if folderPath == 0
    disp('用户取消了文件夹选择');
    return;
end

% 获取文件夹中的所有csv文件
csvFiles = dir(fullfile(folderPath, '*.csv'));

% 如果没有找到csv文件，退出程序
if isempty(csvFiles)
    disp('文件夹中没有找到csv文件');
    return;
end

% 提取文件名中的数字并排序
fileNumbers = zeros(length(csvFiles), 1);
for i = 1:length(csvFiles)
    % 使用正则表达式提取文件名中的数字
    fileName = csvFiles(i).name;
    numbers = regexp(fileName, '\d+', 'match');
    if ~isempty(numbers)
        fileNumbers(i) = str2double(numbers{1});
    end
end

% 根据提取的数字对文件进行排序
[~, sortIdx] = sort(fileNumbers);
csvFiles = csvFiles(sortIdx);

%% 遍历每个csv文件
for i = 1:length(csvFiles)
    if config.enableProgressDisplay
        fprintf('\n=== 处理文件 %d/%d: %s ===\n', i, length(csvFiles), csvFiles(i).name);
    end

    try
        % 获取当前csv文件的完整路径
        currentFileName = fullfile(folderPath, csvFiles(i).name);

        % 读取csv文件
        data = readtable(currentFileName);
        data(1, :) = []; % 删除第一行

        % 验证CSV文件格式
        if size(data, 2) < max(config.requiredColumns)
            warning('csv_lvbo_to_mat_tt:InsufficientColumns', ...
                '文件 %s 列数不足，跳过处理', csvFiles(i).name);
            continue;
        end

        % 提取第二列和第三列数据
        column2 = data{:, config.requiredColumns(1)};
        column3 = data{:, config.requiredColumns(2)};

        % 验证数据有效性
        if any(isnan(column2)) || any(isnan(column3))
            warning('csv_lvbo_to_mat_tt:ContainsNaN', ...
                '文件 %s 包含NaN值，可能影响处理结果', csvFiles(i).name);
        end

        % 生成时间序列
        time = (0:length(column2)-1)' / config.samplingRate;

    %% 使用重构后的双通道处理函数
    % 准备输入信号
    inputSignals = {column2, column3};

    % 调用统一的双通道处理函数
    [processedSignals, snrResults] = processDualChannelSignals(inputSignals, config);

    % 提取处理结果
    column2 = processedSignals{1};
    column3 = processedSignals{2};

    % 显示SNR结果
    if config.enableSNRCalculation
        fprintf('带通滤波后 column2 的 SNR = %.4f dB\n', snrResults.bandpassSNR(1));
        fprintf('带通滤波后 column3 的 SNR = %.4f dB\n', snrResults.bandpassSNR(2));

        if config.enableSpectralSubtraction
            fprintf('谱减滤波后 column2 的 SNR = %.4f dB\n', snrResults.spectralSubtractionSNR(1));
            fprintf('谱减滤波后 column3 的 SNR = %.4f dB\n', snrResults.spectralSubtractionSNR(2));
        end
    end
    
    %% 创建时间表
    tt1 = timetable(column2, 'SampleRate', config.samplingRate);
    tt2 = timetable(column3, 'SampleRate', config.samplingRate);

    % 创建输出文件夹，如果不存在的话
    outputFolder = fullfile(fileparts(mfilename('fullpath')), config.outputFolder);
    if ~exist(outputFolder, 'dir')
        mkdir(outputFolder);
    end

    % 生成新文件名（使用配置的后缀）
    [~, name, ~] = fileparts(currentFileName);
    newFileName = fullfile(outputFolder, [name, config.filenameSuffix, '.mat']);

    % 保存时间表数据为mat文件
    save(newFileName, 'tt1', 'tt2');

    % 可选：保存处理信息和SNR结果
    if config.saveIntermediateResults
        processingInfo = struct();
        processingInfo.config = config;
        processingInfo.snrResults = snrResults;
        processingInfo.processedTime = datetime('now');
        processingInfo.originalFile = csvFiles(i).name;

        infoFileName = fullfile(outputFolder, [name, '_processing_info.mat']);
        save(infoFileName, 'processingInfo');
    end

        % 输出处理进度
        if config.enableProgressDisplay
            disp(['文件处理完成: ', newFileName]);
        end

    catch ME
        if config.enableErrorHandling
            warning('csv_lvbo_to_mat_tt:FileProcessingFailed', ...
                '文件 %s 处理失败: %s', csvFiles(i).name, ME.message);
            fprintf('跳过该文件，继续处理下一个文件...\n');
        else
            rethrow(ME);
        end
    end
end

%% 处理完成总结
if config.enableVerboseOutput
    fprintf('\n=== 批量处理完成 ===\n');
    fprintf('总共处理文件数: %d\n', length(csvFiles));
    fprintf('输出文件夹: %s\n', fullfile(fileparts(mfilename('fullpath')), config.outputFolder));
    fprintf('处理时间: %s\n', char(datetime('now')));
    fprintf('===================\n');
end

%% 清理路径
% 从MATLAB路径中移除添加的函数文件夹
if exist('functionDir', 'var') && exist(functionDir, 'dir')
    rmpath(functionDir);
    fprintf('✓ 已清理函数路径\n');
end